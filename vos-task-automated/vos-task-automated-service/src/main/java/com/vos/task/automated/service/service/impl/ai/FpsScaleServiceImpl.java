package com.vos.task.automated.service.service.impl.ai;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.linker.omos.client.domain.request.cluster.AbilityClusterTpsRequest;
import com.linker.omos.client.domain.request.cluster.ClusterTpsItemRequest;
import com.linker.omos.client.domain.response.cluster.ClusterScheduleConfigResponse;
import com.linker.omos.client.domain.response.cluster.ClusterScheduleItemConfigResponse;
import com.vos.kernel.common.enums.AppTaskCountEnum;
import com.vos.task.automated.api.model.dto.GpuScaleChooseConfig;
import com.vos.task.automated.api.model.entity.ScaleHistoryEntity;
import com.vos.task.automated.api.model.entity.TaskServerRouterEntity;
import com.vos.task.automated.api.model.enums.ScalingModeEnum;
import com.vos.task.automated.api.model.enums.ScalingStatusEnum;
import com.vos.task.automated.api.service.rpc.IServingRouterRpcService;
import com.vos.task.automated.service.entity.AbilityClusterInstanceDTO;
import com.vos.task.automated.service.entity.ClusterAbilityNumDTO;
import com.vos.task.automated.service.entity.FpsScaleConfigDTO;
import com.vos.task.automated.service.service.IFpsScaleService;
import com.vos.task.automated.service.service.IScaleHistoryService;
import com.vos.task.automated.service.service.ISlaveService;
import com.vos.task.automated.service.service.ITaskServerRouterService;
import com.vos.task.automated.service.service.batisplus.IClusterScaleConfigService;
import com.vos.task.automated.service.service.impl.ConcurrentScaleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年11月11日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class FpsScaleServiceImpl implements IFpsScaleService {

    @Resource
    IScaleHistoryService scaleHistoryService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    /**
     * 扩缩容冷却期
     */
    @Value("${deploy.scaleCoolingOffPeriod:300}")
    protected Integer scaleCoolingOffPeriod;

    /**
     * 相同tps配置 扩缩容冷却期倍数冷却期
     */
    @Value("${deploy.sameTpsOffPeriod:3}")
    protected Integer sameTpsOffPeriod;

    /**
     * 算法最大实例总数
     */
    @Value("${deploy.abilityMax:16}")
    protected Integer abilityMax;

    /**
     * 算法最大实例总数
     */
    @Value("${deploy.abilityCallNum:100}")
    protected Integer abilityCallNum;

    /**
     * 扩缩容按照总体tps还是每个集群的tps计算
     */
    @Value("${deploy.scaleByTotalTps:false}")
    protected Boolean scaleByTotalTps;

    @Resource
    IServingRouterRpcService servingRouterRpcService;

    @Resource
    ConcurrentScaleService concurrentScaleService;

    @Resource
    IClusterScaleConfigService clusterScaleConfigService;

    @Resource
    IClusterNodeDrainService clusterNodeDrainService;

    @Resource
    ISlaveService slaveService;

    @Resource
    ITaskServerRouterService taskServerRouterService;

    @Override
    @Async("abilityScaleThreadPool")
    public void checkAbilityScaleByClusterTps(AbilityClusterTpsRequest abilityClusterTpsRequest) {
        Long abilityId = abilityClusterTpsRequest.getAbilityId();
        log.info("{},开始尝试进行tps扩缩容,request:{}", abilityId, JSON.toJSONString(abilityClusterTpsRequest));
        //判断是否需要进行扩缩容
        ScaleHistoryEntity lastScaleHistory = scaleHistoryService.getLastScaleHistory(abilityId);
        if (null != lastScaleHistory) {
            if (ScalingStatusEnum.IN_PROGRESS.getStatus().equals(lastScaleHistory.getStatus())) {
                log.warn("当前算法{},正在扩容中,不进行扩容", abilityId);
                return;
            }
            String updateTime = lastScaleHistory.getUpdateTime();
            LocalDateTime parse = LocalDateTimeUtil.parse(updateTime, "yyyy-MM-dd HH:mm:ss");
            LocalDateTime now = LocalDateTime.now();
            long between = LocalDateTimeUtil.between(parse, now, ChronoUnit.SECONDS);
            //上次扩缩容距离当前大于冷却期
            if (Math.abs(between) < scaleCoolingOffPeriod) {
                log.warn("{},处于扩缩容的冷却期内,冷却期：{}秒，上次执行：{}", abilityId, scaleCoolingOffPeriod, updateTime);
                return;
            }
            if (StrUtil.isNotBlank(lastScaleHistory.getScaleConfig())) {
                FpsScaleConfigDTO fpsScaleConfigDTO = JSON.parseObject(lastScaleHistory.getScaleConfig(), FpsScaleConfigDTO.class);
                String jsonString = JSON.toJSONString(fpsScaleConfigDTO.getClusterTpsItemList());
                String tpsString = JSON.toJSONString(abilityClusterTpsRequest.getClusterTpsItemList());
                if (jsonString.equals(tpsString) && Math.abs(between) < NumberUtil.mul(scaleCoolingOffPeriod.toString(), sameTpsOffPeriod.toString()).longValue()) {
                    log.warn("{},相同tps配置，冷却期：{}秒，上次执行：{}", abilityId, scaleCoolingOffPeriod * sameTpsOffPeriod, updateTime);
                    return;
                }
            }
        }
        //是否处于集群node驱逐阶段，处于跳过
        Set<String> allDrainCluster = clusterNodeDrainService.getAllDrainCluster();
        if (CollectionUtil.isNotEmpty(allDrainCluster)) {
            log.warn("{},存在集群node处于驱逐阶段，暂停处理，{}", abilityId, allDrainCluster);
            return;
        }

        //计算出需要扩缩容的数量
        Double abilityPerFps = servingRouterRpcService.getAbilityPerFps(abilityId.toString());
        if (null == abilityPerFps) {
            log.warn("{},abilityPerFps暂无数据,不做处理", abilityId);
            return;
        }
        //最近一小时调用次数大于100次
//        String nowHour = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH");
//        String callHourMap = AppTaskCountEnum.SUCCESS_COUNT.getKey() + ":" + nowHour + ":" + abilityId;
//        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(callHourMap);
//        if (MapUtil.isEmpty(entries)) {
//            log.warn("{},最近一小时无调用数据,不做处理", abilityId);
//            return;
//        }
//        long sum = entries.values().stream().mapToLong(t -> Long.parseLong(t.toString())).sum();
//        if (sum < abilityCallNum) {
//            log.warn("{},最近一小时调用数据少于{},不做处理", abilityId, abilityCallNum);
//            return;
//        }

        int tpsPerSecond = NumberUtil.ceilDiv(1000, abilityPerFps.intValue());
        Integer concurrent = servingRouterRpcService.getSingleAbilityConcurrent(null, abilityId);
        //一秒内单个算法实例的tps
        tpsPerSecond = tpsPerSecond * concurrent;
        log.info("{},rt:{},tpsPerSecond:{}", abilityId, abilityPerFps, tpsPerSecond);
        //扩容节点需要做调整
        buildAbilityClusterInstance(abilityClusterTpsRequest, tpsPerSecond, abilityPerFps);

    }


    /**
     * 构造集群算法实例分布
     *
     * @return
     */
    private void buildAbilityClusterInstance(AbilityClusterTpsRequest abilityClusterTpsRequest, int abilityTpsPerSecond, Double abilityPerFps) {
        Long abilityId = abilityClusterTpsRequest.getAbilityId();
        //tps从大到小排序
        List<ClusterTpsItemRequest> clusterTpsItemList = abilityClusterTpsRequest.getClusterTpsItemList();
        clusterTpsItemList.sort(Comparator.comparing(ClusterTpsItemRequest::getTps).reversed());


        ClusterScheduleConfigResponse scheduleConfig = clusterScaleConfigService.getScheduleConfig(abilityId);
        log.info("{},scheduleConfig:{}", abilityId, JSON.toJSONString(scheduleConfig));

        FpsScaleConfigDTO fpsScaleConfig = new FpsScaleConfigDTO(abilityPerFps, clusterTpsItemList, scheduleConfig);
        //当前实例副本
        List<TaskServerRouterEntity> copyRouters = concurrentScaleService.getCopyRouters(abilityId);
        ScalingModeEnum scalingModeEnum = ScalingModeEnum.TPS_SCALE;
        //算法安装节点
        String abilityInstallSlave = slaveService.getAbilityInstallSlave(abilityId);
        List<AbilityClusterInstanceDTO> abilityClusterInstanceByTotalTps;
        if (BooleanUtil.isTrue(scaleByTotalTps)) {
            abilityClusterInstanceByTotalTps = totalTpsAndConfig(abilityId, abilityInstallSlave, clusterTpsItemList, abilityTpsPerSecond, copyRouters, scheduleConfig, scalingModeEnum);
        } else {
            abilityClusterInstanceByTotalTps = tpsClusterAndConfig(abilityId, abilityInstallSlave, clusterTpsItemList, abilityTpsPerSecond, copyRouters, scheduleConfig, scalingModeEnum);
        }

        //根据gpu资源重新计算
        checkAbilityClusterInstanceWithConfig(abilityId, abilityClusterInstanceByTotalTps, scalingModeEnum, fpsScaleConfig);


    }


    /**
     * 计算每个集群需要的副本数
     *
     * @param abilityId
     * @param clusterTpsItemList
     * @param abilityTpsPerSecond
     * @param scheduleConfig
     * @return
     */
    private List<AbilityClusterInstanceDTO> tpsClusterAndConfig(Long abilityId, String abilityInstallSlave, List<ClusterTpsItemRequest> clusterTpsItemList, int abilityTpsPerSecond,
                                                                List<TaskServerRouterEntity> copyRouters, ClusterScheduleConfigResponse scheduleConfig, ScalingModeEnum scalingModeEnum) {

        List<ClusterAbilityNumDTO> clusterAbilityNum = slaveService.getClusterAbilityNum(abilityId);
        log.info("{},集群部署实例数量信息:{},部署节点:{}", abilityId, JSON.toJSONString(clusterAbilityNum), abilityInstallSlave);
        List<AbilityClusterInstanceDTO> abilityClusterInstance = new ArrayList<>();

        int total = 0;
        //每个集群tps满足需求
        for (ClusterTpsItemRequest c : clusterTpsItemList) {
            AbilityClusterInstanceDTO abilityClusterInstanceDTO = new AbilityClusterInstanceDTO();
            String slaveAddress = c.getCluster();
            abilityClusterInstanceDTO.setCluster(slaveAddress);
            int instanceNum = NumberUtil.ceilDiv(c.getTps().intValue(), abilityTpsPerSecond);
            if (abilityInstallSlave.equals(slaveAddress)) {
                instanceNum = instanceNum - 1;
            }
            //集群实例配置
            if (scheduleConfig != null && CollectionUtil.isNotEmpty(scheduleConfig.getItemConfigs())) {
                ClusterScheduleItemConfigResponse clusterScheduleItemConfigResponse = scheduleConfig.getItemConfigs().stream().filter(t -> slaveAddress.contains(t.getClusterAddress())).findFirst().orElse(null);
                if (null != clusterScheduleItemConfigResponse) {
                    Integer clusterScaleMax = clusterScheduleItemConfigResponse.getClusterScaleMax();
                    if (clusterScaleMax != null && clusterScaleMax != -1) {
                        clusterScaleMax = abilityInstallSlave.equals(slaveAddress) ? clusterScaleMax - 1 : clusterScaleMax;
                        if (instanceNum > clusterScaleMax) {
                            instanceNum = clusterScaleMax;
                            scalingModeEnum = ScalingModeEnum.CONFIG_SCALE;
                            log.info("{},进入集群最大扩容实例配置 替换：{},scalingModeEnum:{}", abilityId, clusterScaleMax, scalingModeEnum);
                        }
                    }
                    Integer clusterScaleMin = clusterScheduleItemConfigResponse.getClusterScaleMin();
                    if (clusterScaleMin != null && clusterScaleMin != -1) {
                        clusterScaleMin = abilityInstallSlave.equals(slaveAddress) ? clusterScaleMin - 1 : clusterScaleMin;
                        if (instanceNum < clusterScaleMin) {
                            instanceNum = clusterScaleMin;
                            scalingModeEnum = ScalingModeEnum.CONFIG_SCALE;
                            log.info("{},{},进入集群最小扩容实例配置 替换：{},scalingModeEnum:{}", abilityId, slaveAddress, clusterScaleMin, scalingModeEnum);
                        }
                    }
                }
            }
            if (instanceNum < 0) {
                abilityClusterInstanceDTO.setInstanceNumLookForward(0);
                abilityClusterInstance.add(abilityClusterInstanceDTO);
            } else {
                int a = total + instanceNum;
                if (scheduleConfig != null && scheduleConfig.getScaleMax() != null && scheduleConfig.getScaleMax() != -1 && (a + 1) >= scheduleConfig.getScaleMax()) {
                    //配置了最大扩容实例数  计算需要扩容的数
                    instanceNum = scheduleConfig.getScaleMax() - 1 - total;
                    if (instanceNum > 0) {
                        abilityClusterInstanceDTO.setInstanceNumLookForward(instanceNum);
                        abilityClusterInstance.add(abilityClusterInstanceDTO);
                    }
                    scalingModeEnum = ScalingModeEnum.CONFIG_SCALE;
                    break;
                } else {
                    total = a;
                    abilityClusterInstanceDTO.setInstanceNumLookForward(instanceNum);
                    abilityClusterInstance.add(abilityClusterInstanceDTO);
                }
            }
        }

        log.info("{},cluster tps,计算需部署实例信息{}", abilityId, JSON.toJSONString(abilityClusterInstance));
        //副本总数再次校验
//        if (scheduleConfig != null) {
//            Integer scaleMax = scheduleConfig.getScaleMax();
//            Integer scaleMin = scheduleConfig.getScaleMin();
//            int sum = abilityClusterInstance.stream().mapToInt(AbilityClusterInstanceDTO::getInstanceNumLookForward).sum();
//            log.info("{},副本需数量 {},scaleMax{},scaleMin{}", abilityId, sum, scaleMax, scaleMin);
//            int shoudlchange = 0;
//            if (scaleMin != null && scaleMin != -1 && (sum + 1) < scaleMin) {
//                shoudlchange = scaleMin - 1 - sum;
//            }
//            //大于0的部分是需要增加的；
//            if (shoudlchange != 0) {
//                if (shoudlchange > 0) {
//                    scalingModeEnum = ScalingModeEnum.CONFIG_SCALE;
//                    for (AbilityClusterInstanceDTO c : abilityClusterInstance) {
//                        String slaveAddress = c.getCluster();
//                        if (CollectionUtil.isNotEmpty(scheduleConfig.getItemConfigs())) {
//                            ClusterScheduleItemConfigResponse clusterScheduleItemConfigResponse = scheduleConfig.getItemConfigs().stream().filter(t -> slaveAddress.contains(t.getClusterAddress())).findFirst().orElse(null);
//                            if (null != clusterScheduleItemConfigResponse) {
//                                Integer clusterScaleMax = clusterScheduleItemConfigResponse.getClusterScaleMax();
//                                if (clusterScaleMax != null && clusterScaleMax != -1) {
//                                    clusterScaleMax = abilityInstallSlave.equals(slaveAddress) ? clusterScaleMax - 1 : clusterScaleMax;
//                                    int canHold = clusterScaleMax - c.getInstanceNumLookForward();
//                                    if (canHold >= shoudlchange) {
//                                        c.setInstanceNumLookForward(shoudlchange + c.getInstanceNumLookForward());
//                                        break;
//                                    } else {
//                                        c.setInstanceNumLookForward(clusterScaleMax);
//                                        shoudlchange = shoudlchange - canHold;
//                                    }
//                                } else {
//                                    c.setInstanceNumLookForward(shoudlchange + c.getInstanceNumLookForward());
//                                    break;
//                                }
//                            } else {
//                                c.setInstanceNumLookForward(shoudlchange + c.getInstanceNumLookForward());
//                                break;
//                            }
//                        } else {
//                            c.setInstanceNumLookForward(shoudlchange + c.getInstanceNumLookForward());
//                            break;
//                        }
//                    }
//                }
//            }
//        }
//        log.info("{},cluster tps,计算需部署实例信息{}", abilityId, JSON.toJSONString(abilityClusterInstance));
        //排除掉不符合预期的实例 移除
        checkAbilityClusterInstance(abilityClusterInstance, copyRouters);
        log.info("{},cluster tps,计算需部署实例信息{},scalingModeEnum:{}", abilityId, JSON.toJSONString(abilityClusterInstance), scalingModeEnum);
        return abilityClusterInstance;
    }

    /**
     * 根据总tps及配置计算
     *
     * @param abilityId
     * @param clusterTpsItemList
     * @param abilityTpsPerSecond
     * @param copyRouters
     * @param scheduleConfig
     * @return
     */
    private List<AbilityClusterInstanceDTO> totalTpsAndConfig(Long abilityId, String abilityInstallSlave, List<ClusterTpsItemRequest> clusterTpsItemList, int abilityTpsPerSecond,
                                                              List<TaskServerRouterEntity> copyRouters, ClusterScheduleConfigResponse scheduleConfig, ScalingModeEnum scalingModeEnum) {
        //总tps
        Long sum = clusterTpsItemList.stream().mapToLong(ClusterTpsItemRequest::getTps).sum();
        List<AbilityClusterInstanceDTO> abilityClusterInstance = new ArrayList<>();
        //总体tps满足要求即可 减少gpu资源利用
        int instanceNum = NumberUtil.ceilDiv(sum.intValue(), abilityTpsPerSecond);
        if (instanceNum > abilityMax) {
            log.warn("{},扩容副本数量计算值大于系统设置最大值:{}", abilityId, abilityMax);
            instanceNum = abilityMax;
        }
        log.info("{},tps 总数：{},单个算法实例的tps:{},需要实例数：{}", abilityId, sum, abilityTpsPerSecond, instanceNum);
        if (scheduleConfig != null) {
            Integer scaleMin = scheduleConfig.getScaleMin();
            Integer scaleMax = scheduleConfig.getScaleMax();
            if (scaleMin != null && scaleMin != -1 && instanceNum < scaleMin) {
                instanceNum = scaleMin;
                log.info("{},进入最小扩容实例配置 替换：{}", abilityId, scaleMin);
                scalingModeEnum = ScalingModeEnum.CONFIG_SCALE;
            }
            if (scaleMax != null && scaleMax != -1 && instanceNum > scaleMax) {
                instanceNum = scaleMax;
                scalingModeEnum = ScalingModeEnum.CONFIG_SCALE;
                log.info("{},进入最大扩容实例配置 替换：{}", abilityId, scaleMax);
            }
        }
        instanceNum = instanceNum - 1;
        //当前节点实例数量
        if (copyRouters.size() == instanceNum) {
            log.warn("{},算法实例数满足需求，副本数：{},无需处理", instanceNum, abilityId);
            return abilityClusterInstance;
        } else {
            int left = instanceNum;
            for (ClusterTpsItemRequest c : clusterTpsItemList) {
                if (left == 0) {//无有剩余实例 跳出循环
                    break;
                }
                String slaveAddress = c.getCluster();
                AbilityClusterInstanceDTO abilityClusterInstanceDTO = new AbilityClusterInstanceDTO();
                abilityClusterInstanceDTO.setCluster(slaveAddress);
                int shouldNum = NumberUtil.mul(instanceNum, NumberUtil.div(c.getTps(), sum)).intValue();
                if (scheduleConfig != null && CollectionUtil.isNotEmpty(scheduleConfig.getItemConfigs())) {
                    ClusterScheduleItemConfigResponse clusterScheduleItemConfigResponse = scheduleConfig.getItemConfigs().stream().filter(t -> slaveAddress.contains(t.getClusterAddress())).findFirst().orElse(null);
                    if (null != clusterScheduleItemConfigResponse) {
                        Integer clusterScaleMin = clusterScheduleItemConfigResponse.getClusterScaleMin();
                        Integer clusterScaleMax = clusterScheduleItemConfigResponse.getClusterScaleMax();
                        if (clusterScaleMin != null && clusterScaleMin != -1) {
                            clusterScaleMin = abilityInstallSlave.equals(clusterScheduleItemConfigResponse.getClusterAddress()) ? clusterScaleMin - 1 : clusterScaleMin;
                            if (shouldNum < clusterScaleMin) {
                                shouldNum = clusterScaleMin;
                                log.info("{},{},进入集群最小扩容实例配置 替换：{}", abilityId, slaveAddress, clusterScaleMin);
                                scalingModeEnum = ScalingModeEnum.CONFIG_SCALE;
                            }
                        }
                        if (clusterScaleMax != null && clusterScaleMax != -1) {
                            clusterScaleMax = abilityInstallSlave.equals(clusterScheduleItemConfigResponse.getClusterAddress()) ? clusterScaleMax - 1 : clusterScaleMax;
                            if (shouldNum > clusterScaleMax) {
                                shouldNum = clusterScaleMax;
                                log.info("{},进入集群最大扩容实例配置 替换：{}", abilityId, clusterScaleMax);
                                scalingModeEnum = ScalingModeEnum.CONFIG_SCALE;
                            }
                        }
                    }
                }
                if (left < shouldNum) {
                    shouldNum = left;
                }
                abilityClusterInstanceDTO.setInstanceNumLookForward(shouldNum);
                left = left - shouldNum;
                if (shouldNum > 0) {
                    abilityClusterInstance.add(abilityClusterInstanceDTO);
                }
            }
            log.info("abilityId{},总tps,计算需部署实例信息{},left:{},scalingModeEnum:{}", abilityId, JSON.toJSONString(abilityClusterInstance), left, scalingModeEnum);
            if (left > 0) {
                log.warn("abilityId{},left还有剩余未被分配:{}", abilityId, left);
                // todo
            }
        }
        // 计算每个集群需要的gpu数量
        checkAbilityClusterInstance(abilityClusterInstance, copyRouters);
        return abilityClusterInstance;
    }


    /**
     * 计算每个集群需要的gpu数量
     *
     * @param abilityClusterInstance
     * @param copyRouters
     */
    private void checkAbilityClusterInstance(List<AbilityClusterInstanceDTO> abilityClusterInstance, List<TaskServerRouterEntity> copyRouters) {

        //需要移除的
        if (CollectionUtil.isNotEmpty(copyRouters)) {
            Map<String, List<TaskServerRouterEntity>> copyRoutersGroup = copyRouters.stream().collect(Collectors.groupingBy(TaskServerRouterEntity::getSlaveAddress));
            Set<String> cluseterSet = abilityClusterInstance.stream().map(AbilityClusterInstanceDTO::getCluster).collect(Collectors.toSet());
            // 计算每个集群需要的gpu数量
            for (AbilityClusterInstanceDTO i : abilityClusterInstance) {
                if (copyRoutersGroup.containsKey(i.getCluster())) {
                    i.setInstanceNow(copyRoutersGroup.get(i.getCluster()).size());
                }
            }
            //不符合预期的实例 移除
            Set<String> cluseterOldSet = copyRouters.stream().map(TaskServerRouterEntity::getSlaveAddress).collect(Collectors.toSet());
            Collection<String> subtract = CollectionUtil.subtract(cluseterOldSet, cluseterSet);
            if (CollectionUtil.isNotEmpty(subtract)) {
                for (String s : subtract) {
                    AbilityClusterInstanceDTO abilityClusterInstanceDTO = new AbilityClusterInstanceDTO();
                    abilityClusterInstanceDTO.setCluster(s);
                    abilityClusterInstanceDTO.setInstanceNumLookForward(0);
                    abilityClusterInstanceDTO.setInstanceNow(copyRoutersGroup.get(s).size());
                    abilityClusterInstance.add(abilityClusterInstanceDTO);
                }
            }
        }
    }


    /**
     * 远程配置获取
     *
     * @param abilityClusterInstance
     */
    private void checkAbilityClusterInstanceWithConfig(Long abilityId, List<AbilityClusterInstanceDTO> abilityClusterInstance, ScalingModeEnum scalingModeEnum, FpsScaleConfigDTO fpsScaleConfig) {
        //gpu资源计算最终结果
        if (CollectionUtil.isNotEmpty(abilityClusterInstance)) {
            TaskServerRouterEntity taskServerRouterEntity = taskServerRouterService.getInstallRouteByAbilityId(abilityId.toString());
            String modelId = taskServerRouterEntity.getModelId();
            if (StrUtil.isNotBlank(modelId)) {
                log.warn("九头蛇算法暂时不支持tps扩缩容");
                return;
            }

            //需要扩容的实例
            List<AbilityClusterInstanceDTO> shouldAdd = abilityClusterInstance.stream().filter(t -> t.getInstanceNumLookForward() > t.getInstanceNow()).collect(Collectors.toList());
            //需要缩容的实例
            List<AbilityClusterInstanceDTO> shouldReduce = abilityClusterInstance.stream().filter(t -> t.getInstanceNumLookForward() < t.getInstanceNow()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(shouldAdd)) {
                log.info("{},待扩容集群信息:{}", abilityId, JSON.toJSONString(shouldAdd));

                int failNum = 0;
                //占用资源进行扩容
                for (AbilityClusterInstanceDTO a : shouldAdd) {
                    int shouldScaleNum = NumberUtil.sub(a.getInstanceNumLookForward(), a.getInstanceNow()).intValue();
                    GpuScaleChooseConfig gpuScaleChooseConfig = new GpuScaleChooseConfig();
                    gpuScaleChooseConfig.setMachineGroup(a.getCluster());
                    ScaleHistoryEntity scaleHistoryEntity = buildScaleHistoryEntity(abilityId, a, scalingModeEnum, JSON.toJSONString(fpsScaleConfig));
                    concurrentScaleService.concurrentExpandAbilityPod(taskServerRouterEntity, scaleHistoryEntity, gpuScaleChooseConfig, shouldScaleNum);
                    int actualNodeCount = scaleHistoryEntity.getActualNodeCount();
                    failNum += (shouldScaleNum - actualNodeCount);
                }

                if (failNum == 0) {
                    //扩容无失败直接尝试缩容
                    reduceClusterSlaves(abilityId, shouldReduce, taskServerRouterEntity, scalingModeEnum, fpsScaleConfig);
                } else {
                    int sum = shouldReduce.stream().mapToInt(t -> NumberUtil.sub(t.getInstanceNow(), t.getInstanceNumLookForward()).intValue()).sum();
                    int a = sum - failNum;
                    if (a >= 0) {
                        //尝试缩容的量满足扩容量
                        log.info("{},扩容失败后待继续扩容数：{},待缩容数量：{}", abilityId, failNum, sum);
                        List<AbilityClusterInstanceDTO> shouldReduceLast = new ArrayList<>();
                        int reduceNumTotal = 0;
                        boolean t = false;
                        for (AbilityClusterInstanceDTO r : shouldReduce) {
                            if (t) {
                                shouldReduceLast.add(r);
                            } else {
                                Integer reduceNum = NumberUtil.sub(r.getInstanceNow(), r.getInstanceNumLookForward()).intValue();
                                reduceNumTotal += reduceNum;
                                if (reduceNumTotal > failNum) {
                                    t = true;
                                    reduceNum = reduceNumTotal - failNum;
                                    r.setInstanceNumLookForward(NumberUtil.sub(r.getInstanceNow(), reduceNum).intValue());
                                    shouldReduceLast.add(r);
                                }
                            }
                        }
                        reduceClusterSlaves(abilityId, shouldReduceLast, taskServerRouterEntity, scalingModeEnum, fpsScaleConfig);
                    } else {
                        int tryScaleByGpu = Math.abs(a);
                        log.info("{},尝试找最空资源扩容量：{}", abilityId, tryScaleByGpu);
                        AbilityClusterInstanceDTO abilityClusterInstanceTry = new AbilityClusterInstanceDTO();
                        abilityClusterInstanceTry.setInstanceNow(0);
                        abilityClusterInstanceTry.setInstanceNumLookForward(tryScaleByGpu);
                        ScaleHistoryEntity scaleHistoryEntity = buildScaleHistoryEntity(abilityId, abilityClusterInstanceTry, scalingModeEnum, JSON.toJSONString(fpsScaleConfig));
                        concurrentScaleService.concurrentExpandAbilityPod(taskServerRouterEntity, scaleHistoryEntity, new GpuScaleChooseConfig(), tryScaleByGpu);
                    }
                }
            } else {
                reduceClusterSlaves(abilityId, shouldReduce, taskServerRouterEntity, scalingModeEnum, fpsScaleConfig);
            }
        }
    }


    /**
     * 缩容
     *
     * @param abilityId
     * @param shouldReduce
     * @param taskServerRouterEntity
     * @param scalingModeEnum
     * @param fpsScaleConfig
     */
    private void reduceClusterSlaves(Long abilityId, List<AbilityClusterInstanceDTO> shouldReduce, TaskServerRouterEntity taskServerRouterEntity, ScalingModeEnum scalingModeEnum, FpsScaleConfigDTO fpsScaleConfig) {
        if (CollectionUtil.isNotEmpty(shouldReduce)) {
            log.info("{},待缩容集群信息:{}", abilityId, JSON.toJSONString(shouldReduce));
            for (AbilityClusterInstanceDTO a : shouldReduce) {
                ScaleHistoryEntity scaleHistoryEntity = buildScaleHistoryEntity(abilityId, a, scalingModeEnum, JSON.toJSONString(fpsScaleConfig));
                concurrentScaleService.concurrentShrinkAbilityPod(a.getCluster(), taskServerRouterEntity, null, false, scaleHistoryEntity, NumberUtil.sub(a.getInstanceNow(), a.getInstanceNumLookForward()).intValue());
            }
        }
    }

    /**
     * 构建历史记录
     *
     * @param abilityId
     * @param a
     * @param scalingModeEnum
     * @return
     */
    private ScaleHistoryEntity buildScaleHistoryEntity(Long abilityId, AbilityClusterInstanceDTO a, ScalingModeEnum scalingModeEnum, String scaleConfig) {
        ScaleHistoryEntity scaleHistoryEntity = new ScaleHistoryEntity();
        scaleHistoryEntity.setScalingMode(scalingModeEnum.getMode());
        scaleHistoryEntity.setAbilityId(abilityId.toString());
        scaleHistoryEntity.setCurrentNodeCount(a.getInstanceNow());
        scaleHistoryEntity.setExpectedNodeCount(a.getInstanceNumLookForward());
        scaleHistoryEntity.setScaleConfig(scaleConfig);
        scaleHistoryEntity.setUpdateTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        scaleHistoryEntity.setStatus(ScalingStatusEnum.IN_PROGRESS.getStatus());
        scaleHistoryService.save(scaleHistoryEntity);
        return scaleHistoryEntity;
    }


}
