package com.vos.task.poll.service.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.linker.omos.client.domain.Ratio;
import com.vos.kernel.common.common.LifecycleAware;
import com.vos.task.manage.api.model.dto.LocalMessageQueueDTO;
import com.vos.task.poll.service.config.AbilityScheduleProperties;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.agrona.concurrent.AbstractConcurrentArrayQueue;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年10月31日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class RatioAbilityQueue implements LifecycleAware {

    /**
     * 算法编号
     */
    private final String abilityId;

    /**
     * 算法编号
     */
    private final String abilityCode;

    private String topicName;

    /**
     * 算法调用配置
     */
    protected AbilityCallConfig abilityCallConfig;

    /**
     * 队列容量：并发数*系数
     */
    private Integer capacity;

    /**
     * 比例队列容量
     */
    private Integer ratioQueueCap = 0;

    /**
     * 容量队列
     */
    BlockingQueue<Integer> capacityQueue = new ArrayBlockingQueue<>(512);


    /**
     * 优先队列数量
     */
    private AtomicLong priorityQueueNum = new AtomicLong(0);

    /**
     * 比例队列数量
     */
    private AtomicLong ratioQueueNum = new AtomicLong(0);

    /**
     * 并发数
     */
    private Integer concurrentLimitNum = 1;

    /**
     * ingress 集群
     */
    private Set<String> clusters = new HashSet<>(50);

    /**
     * 最大的队列index
     */
    private final Integer lastQueueIndex;


    /**
     * 算法队列策略内容
     */
    List<Ratio> abilityQueueStrategy;

    /**
     * 配置
     */
    AbilityScheduleProperties abilityScheduleProperties;

    /**
     * 保存数据
     */
    IMessageQueueOffSetService messageQueueOffSetService;

    /**
     * 是否在运行
     */
    private final AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 集群下的算法队列
     */
    List<RatioAbilityConcurrentQueue<Message>> ratioAbilityQueueMap = new ArrayList<>();

    /**
     * 算法统计容器
     */
    private AbilityStatistician abilityMetric;


    /**
     * 构造函数
     */
    public RatioAbilityQueue(String topicName, AbilityCallConfig abilityCallConfig, String abilityId, IMessageQueueOffSetService messageQueueOffSetService,
                             String abilityCode, String abilityQueueStrategyInfo, AbilityScheduleProperties abilityScheduleProperties) {
        this.abilityId = abilityId;
        this.abilityCode = abilityCode;
        this.topicName = topicName;
        this.abilityCallConfig = abilityCallConfig;
        log.info("abilityQueueStrategyInfo:{}", abilityQueueStrategyInfo);
        this.abilityQueueStrategy = JSON.parseArray(abilityQueueStrategyInfo, Ratio.class);
        this.abilityScheduleProperties = abilityScheduleProperties;
        this.messageQueueOffSetService = messageQueueOffSetService;
        this.lastQueueIndex = abilityScheduleProperties.getRatioQueueSize() - 1;
    }

    /**
     * 设置容量
     *
     * @param capacity
     */
    public void setCapacity(Integer capacity, Set<String> clusters) {
        this.concurrentLimitNum = capacity;
        int cap = NumberUtil.mul(new BigDecimal(this.abilityCallConfig.getPollCapacityRatio().toString()), capacity).intValue();
        cap = Math.max(cap, 64);
        this.capacity = cap;
        this.ratioQueueCap = NumberUtil.ceilDiv(cap, 3);
        this.capacityQueue = new ArrayBlockingQueue<>(NumberUtil.mul(new BigDecimal(this.abilityCallConfig.getPollCapacityRatio().toString()), cap).intValue());
        this.clusters.addAll(clusters);
        //初始化集群下的算法队列
        this.clusters.add("default");
        for (String cluster : this.clusters) {
            for (int i = 0; i < this.abilityScheduleProperties.getRatioQueueSize(); i++) {
                RatioAbilityConcurrentQueue<Message> messages = new RatioAbilityConcurrentQueue<>(cluster, i, 1024);
                this.ratioAbilityQueueMap.add(messages);
            }
        }
        this.onStart();
    }

    /**
     * 设置算法统计容器
     *
     * @param abilityMetric
     */
    public void setAbilityMetric(AbilityStatistician abilityMetric) {
        this.abilityMetric = abilityMetric;
    }

    /**
     * 根据当前本地队列容量判断是否还需要拉取任务
     *
     * @return
     */
    public Boolean enableToPullTask(Integer queueIndex) {
        if (!running.get()) {
            return false;
        }
        if (queueIndex == 0) {
            //最高级队列
            return capacityQueue.size() < this.capacity;
        } else if (queueIndex.equals(this.lastQueueIndex)) {
            //潮汐队列
            return priorityQueueNum.get() <= 0 && ratioQueueNum.get() <= 0 && (capacityQueue.size() < this.capacity);
        } else {
            //比例队列 priorityQueueNum.get() == 0 &&
            return (capacityQueue.size() < this.capacity) && (this.ratioAbilityQueueMap.stream().filter(t -> t.getQueueId()
                    .equals(queueIndex)).mapToInt(AbstractConcurrentArrayQueue::size).sum() < this.ratioQueueCap);
        }
    }


    /**
     * rocket拉取任务添加
     *
     * @param mqMessage
     */
    public void addRocketTask(Integer queueIndex, List<MessageExt> mqMessage) {
        if (!running.get()) {
            log.warn("处理线称未启动，跳过处理");
            return;
        }
        //筛选出对应queueId
        List<RatioAbilityConcurrentQueue<Message>> queueList = this.ratioAbilityQueueMap.stream().filter(t -> t.getQueueId().equals(queueIndex)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(queueList)) {
            throw new RuntimeException("queueIndex is not exist");
        }
        for (MessageExt messageExt : mqMessage) {
            //根据集群信息获取queue
            String tags = StrUtil.isBlank(messageExt.getTags()) ? "default" : messageExt.getTags();
            if (log.isDebugEnabled()) {
                log.debug("queueIndex:{},cluster:{}", queueIndex, tags);
            }
            RatioAbilityConcurrentQueue<Message> queue = queueList.stream().filter(t -> t.getCluster().equals(tags)).findFirst()
                    .orElseGet(() -> CollectionUtil.getFirst(queueList));
            //激活
            this.capacityQueue.add(1);
            //添加到队列中
            queue.add(messageExt);
            if (queueIndex == 0) {
                this.priorityQueueNum.incrementAndGet();
            } else if (!queueIndex.equals(this.lastQueueIndex)) {
                this.ratioQueueNum.incrementAndGet();
            }
        }
    }


    /**
     * 清除本地队列
     */
    public void clear() {
        running.set(false);
        this.ratioAbilityQueueMap = new ArrayList<>();
        for (String cluster : this.clusters) {
            for (int i = 0; i < this.abilityScheduleProperties.getRatioQueueSize(); i++) {
                RatioAbilityConcurrentQueue<Message> messages = new RatioAbilityConcurrentQueue<>(cluster, i, 1024);
                this.ratioAbilityQueueMap.add(messages);
            }
        }
        this.capacityQueue.clear();
        this.priorityQueueNum.set(0);
        this.ratioQueueNum.set(0);
        running.set(true);
    }

    /**
     * 获取本地队列信息
     *
     * @return
     */
    public LocalMessageQueueDTO getLocalQueueInfo() {

        LocalMessageQueueDTO localMessageQueue = new LocalMessageQueueDTO();
        localMessageQueue.setAbilityId(abilityId);
        localMessageQueue.setAbilityCode(abilityCode);
        localMessageQueue.setCapacity(Long.valueOf(capacity));
        localMessageQueue.setLineCount((long) this.capacityQueue.size());
        if (this.abilityMetric != null) {
            localMessageQueue.setRt(this.abilityMetric.getAbilityAvgRt());
            localMessageQueue.setTps(this.abilityMetric.getAbilityTps());
//            log.info("rt:{},tps:{}", localMessageQueue.getRt(), localMessageQueue.getTps());
        } else {
            log.info("abilityMetric is null");
        }

        List<Dict> details = new ArrayList<>();
        for (RatioAbilityConcurrentQueue<Message> l : ratioAbilityQueueMap) {
            int size = l.size();
            if (size > 0) {
                Dict dict = new Dict();
                dict.put("queueId", l.getQueueId());
                dict.put("cluster", l.getCluster());
                dict.put("lineCount", size);
                details.add(dict);
            }
        }
        localMessageQueue.setDetails(details);
        return localMessageQueue;
    }

    /**
     * 处理消息：直接发送内存队列数据增加链路追踪
     */
    public Message addDirectMessageTrace(Message messageExt) {
        return messageExt;
    }

    /**
     * 判定并放入到本地队列
     * 添加synchronized 防止容量队列溢出
     *
     * @param queueIndex
     * @param taskSubEncodeMessage
     * @return
     */
    public synchronized Boolean checkAndScheduleToThread(Integer queueIndex, String tag, List<String> taskSubEncodeMessage) {
        if (!running.get()) {
            return true;
        }
        if (queueIndex >= abilityScheduleProperties.getRatioQueueSize()) {
            throw new RuntimeException("taskOrder 应小于队列数");
        }
        if (queueIndex == 0) {
            //判定是否需要
            if (capacityQueue.size() < this.capacity) {
                return sendToThread(queueIndex, tag, taskSubEncodeMessage);
            } else {
                return true;
            }
        } else if (queueIndex.equals(this.lastQueueIndex)) {
            if (priorityQueueNum.get() <= 0 && ratioQueueNum.get() <= 0 && (capacityQueue.size() < this.capacity)) {
                return sendToThread(queueIndex, tag, taskSubEncodeMessage);
            } else {
                return true;
            }
        } else {
            //mq中优先队列存在
            if ((capacityQueue.size() < this.capacity)) {
                int queueSize = this.ratioAbilityQueueMap.stream().filter(t -> t.getQueueId()
                        .equals(queueIndex)).mapToInt(AbstractConcurrentArrayQueue::size).sum();
                if (queueSize < this.ratioQueueCap) {
                    return sendToThread(queueIndex, tag, taskSubEncodeMessage);
                } else {
                    return true;
                }
            } else {
                return true;
            }
        }
    }


    /**
     * 发送到线程
     *
     * @param queueIndex
     * @param taskSubEncodeMessage
     * @return
     */
    private Boolean sendToThread(Integer queueIndex, String tag, List<String> taskSubEncodeMessage) {
        log.info("{}下发任务，{},直接调度到处理线程,capacityQueueS:{},cap:{},tag:{}", abilityId, queueIndex, capacityQueue.size(), capacity, tag);
        List<MessageExt> messages = new ArrayList<>();
        for (String msgBody : taskSubEncodeMessage) {
            MessageExt messageExt = new MessageExt();
            messageExt.setTags(tag);
            messageExt.setTopic(topicName);
            messageExt.setBody(msgBody.getBytes());
            addDirectMessageTrace(messageExt);
            messages.add(messageExt);
        }
        addRocketTask(queueIndex, messages);
        return false;
    }

    @Override
    public void onStart() {
        if (!running.compareAndSet(false, true)) {
            throw new IllegalStateException("RatioAbilityQueue " + abilityCode + " already running");
        }
        //加载之前遗留在内存中的数据
//        List<Message> messages = messageQueueOffSetService.loadCache(abilityId);
//        if (CollectionUtil.isNotEmpty(messages)) {
//
//        }
    }

    @Override
    public void onShutdown() {
        log.info("准备关闭RatioAbilityQueue {}", abilityCode);
        running.set(false);
        //将内存中的数据保存到缓存
//        if (this.capacityQueue.size() > 0) {
//            List<Message> list = new ArrayList<>();
//            for (RatioAbilityConcurrentQueue<Message> queue : ratioAbilityQueueMap) {
//                if (queue.size() > 0) {
//                    List<Message> taskSubsApi = ListUtil.toList(queue);
//                    list.addAll(taskSubsApi);
//                }
//            }
//            messageQueueOffSetService.saveCache(abilityId, list);
//        }

    }


    /**
     * processor拉取消息
     * 没有消息则阻塞
     * cluster :default 或者具体的ingress
     *
     * @return
     */
    public Message poll(String cluster) throws InterruptedException {
        //关闭状态
        if (!running.get()) {
            return null;
        }
        //阻塞获取通行证
        this.capacityQueue.take();
        //获取真正的消息
        Optional<RatioAbilityConcurrentQueue<Message>> sameClusterPriorityQueue = ratioAbilityQueueMap.stream().filter(t -> t.getCluster().equals(cluster) && t.getQueueId() == 0).findFirst();
        if (sameClusterPriorityQueue.isPresent()) {
            //同集群优先队列
            Message poll = sameClusterPriorityQueue.get().poll();
            if (null != poll) {
                this.priorityQueueNum.decrementAndGet();
                return poll;
            } else {
                //所有集群优先队列获取
                return pollRatioMessage(cluster);
            }
        } else {
            return pollRatioMessage(cluster);
        }

    }


    /**
     * 比例拉取
     *
     * @return
     */
    private Message pollRatioMessage(String cluster) {

        //所有集群优先队列获取
        List<RatioAbilityConcurrentQueue<Message>> priorityQueue = ratioAbilityQueueMap.stream().filter(t -> t.getQueueId() == 0).collect(Collectors.toList());
        for (RatioAbilityConcurrentQueue<Message> queue : priorityQueue) {
            Message poll1 = queue.poll();
            if (poll1 != null) {
                this.priorityQueueNum.decrementAndGet();
                return poll1;
            }
        }
        //所有集群比例队列获取
        int queueIndex = getRandomValue();
        List<RatioAbilityConcurrentQueue<Message>> ratioQueues = ratioAbilityQueueMap.stream().filter(t -> t.getCluster().equals(cluster) && t.getQueueId() == queueIndex && t.size() > 0).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ratioQueues)) {
            ratioQueues = ratioAbilityQueueMap.stream().filter(t -> t.getQueueId() == queueIndex).collect(Collectors.toList());
        }
        for (RatioAbilityConcurrentQueue<Message> queue : ratioQueues) {
            Message poll1 = queue.poll();
            if (poll1 != null) {
                this.ratioQueueNum.decrementAndGet();
                return poll1;
            }
        }
        List<RatioAbilityConcurrentQueue<Message>> ratioQueueList = ratioAbilityQueueMap.stream().filter(t -> t.getCluster().equals(cluster) && t.getQueueId() != 0 && !t.getQueueId().equals(lastQueueIndex) && t.size() > 0).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ratioQueueList)) {
            ratioQueueList = ratioAbilityQueueMap.stream().filter(t -> t.getQueueId() != 0 && !t.getQueueId().equals(lastQueueIndex)).collect(Collectors.toList());
        }
        for (RatioAbilityConcurrentQueue<Message> queue : ratioQueueList) {
            Message poll1 = queue.poll();
            if (poll1 != null) {
                this.ratioQueueNum.decrementAndGet();
                return poll1;
            }
        }
        List<RatioAbilityConcurrentQueue<Message>> lastQueueList = ratioAbilityQueueMap.stream().filter(t -> t.getQueueId().equals(lastQueueIndex)).collect(Collectors.toList());
        for (RatioAbilityConcurrentQueue<Message> queue : lastQueueList) {
            Message poll1 = queue.poll();
            if (poll1 != null) {
                return poll1;
            }
        }
        return null;
    }


    /**
     * 根据参数中的权重列表返回一个随机值queueId
     *
     * @return
     */
    public int getRandomValue() {
        List<Ratio> ratioConfig = this.abilityQueueStrategy;
        Random random = new Random();
        int totalWeight = 0;
        for (Ratio ratio : ratioConfig) {
            totalWeight += ratio.getRatio();
        }
        int randomValue = random.nextInt(totalWeight);
        int cumulativeWeight = 0;
        for (Ratio ratio : ratioConfig) {
            cumulativeWeight += ratio.getRatio();
            if (randomValue < cumulativeWeight) {
                return ratio.getQueueId();
            }
        }
        return ratioConfig.get(ratioConfig.size() - 1).getQueueId();
    }

}
