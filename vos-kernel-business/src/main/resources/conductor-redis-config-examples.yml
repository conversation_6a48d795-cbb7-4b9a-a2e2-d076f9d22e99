# Conductor Redis 配置示例
# 支持单机、主从、集群模式，兼容 Jedis 和 Lettuce 客户端

conductor:
  redis:
    # 是否启用 conductor redis 功能
    enable: true
    # workflow 命名空间前缀
    workflow-namespace-prefix: "conductor"
    # 队列命名空间前缀
    queue-namespace-prefix: "conductor_queue"
    # 键空间域名
    keyspace-domain: "workflow"
    # 环境栈名称
    stack: "prod"
    
    # Redis 连接配置
    conductor-redis:
      # 数据库索引
      database: 0
      # 连接超时时间
      timeout: 5000ms
      
      # ===== 单机模式配置 =====
      host: localhost
      port: 6379
      password: your_password
      
      # ===== 集群模式配置 =====
      # cluster:
      #   nodes:
      #     - *************:7000
      #     - *************:7001
      #     - *************:7002
      #     - *************:7000
      #     - *************:7001
      #     - *************:7002
      #   max-redirects: 3
      
      # ===== 哨兵模式配置 =====
      # sentinel:
      #   master: mymaster
      #   nodes:
      #     - *************:26379
      #     - *************:26379
      #     - *************:26379
      #   password: sentinel_password
      
      # ===== Jedis 连接池配置 =====
      jedis:
        pool:
          # 连接池最大连接数
          max-active: 20
          # 连接池最大空闲连接数
          max-idle: 10
          # 连接池最小空闲连接数
          min-idle: 5
          # 连接池最大等待时间
          max-wait: 3000ms
      
      # ===== Lettuce 连接池配置 =====
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 20
          # 连接池最大空闲连接数
          max-idle: 10
          # 连接池最小空闲连接数
          min-idle: 5
          # 连接池最大等待时间
          max-wait: 3000ms

---
# 开发环境配置示例
spring:
  profiles: dev
  
conductor:
  redis:
    enable: true
    workflow-namespace-prefix: "conductor_dev"
    stack: "dev"
    conductor-redis:
      host: localhost
      port: 6379
      database: 1
      timeout: 3000ms
      jedis:
        pool:
          max-active: 10
          max-idle: 5
          min-idle: 2
          max-wait: 2000ms

---
# 生产环境集群配置示例
spring:
  profiles: prod
  
conductor:
  redis:
    enable: true
    workflow-namespace-prefix: "conductor_prod"
    stack: "prod"
    conductor-redis:
      timeout: 5000ms
      password: ${REDIS_PASSWORD}
      cluster:
        nodes:
          - ${REDIS_NODE_1}:7000
          - ${REDIS_NODE_1}:7001
          - ${REDIS_NODE_1}:7002
          - ${REDIS_NODE_2}:7000
          - ${REDIS_NODE_2}:7001
          - ${REDIS_NODE_2}:7002
        max-redirects: 3
      lettuce:
        pool:
          max-active: 50
          max-idle: 20
          min-idle: 10
          max-wait: 5000ms

---
# 哨兵模式配置示例
spring:
  profiles: sentinel
  
conductor:
  redis:
    enable: true
    workflow-namespace-prefix: "conductor_sentinel"
    stack: "prod"
    conductor-redis:
      timeout: 5000ms
      password: ${REDIS_PASSWORD}
      sentinel:
        master: mymaster
        password: ${SENTINEL_PASSWORD}
        nodes:
          - ${SENTINEL_NODE_1}:26379
          - ${SENTINEL_NODE_2}:26379
          - ${SENTINEL_NODE_3}:26379
      lettuce:
        pool:
          max-active: 30
          max-idle: 15
          min-idle: 5
          max-wait: 3000ms
